import { IntentMatch, IntentResponse, ConversationContext } from './types';

// Intent Detection System
export const IntentDetector = {
  // Intent patterns with keywords and phrases
  intents: {
    yes: {
      patterns: [
        /\b(yes|yeah|yup|sure|absolutely|of course|definitely|why not|ok|okay|sounds good|alright|right|correct|true|agree|exactly|indeed|certainly|perfect|great|awesome|good|fine)\b/i,
        /\b(let's do it|go ahead|go for it|i'm in|count me in|sign me up)\b/i,
        /\b(i do|i would|i will)\b(?!\s+(want|need|like|make|have|get|ask|know|think|believe))/i,
        /^(i do!?|yes!|sure!|absolutely!|definitely!)$/i
      ],
      confidence: 0.8
    },
    no: {
      patterns: [
        /\b(no|nope|nah|not really|don't think so|pass|never|negative|disagree|wrong|false|incorrect)\b/i,
        /\b(not interested|no thanks|no thank you|not now|maybe later|not today)\b/i
      ],
      confidence: 0.8
    },
    greeting: {
      patterns: [
        /\b(hi|hello|hey|good morning|good afternoon|good evening|greetings|howdy|what's up|sup)\b/i,
        /\b(how are you|how's it going|nice to meet you)\b/i
      ],
      confidence: 0.9
    },
    goodbye: {
      patterns: [
        /\b(bye|goodbye|see you|cya|later|farewell|adios|take care|have a good day|talk soon)\b/i,
        /\b(gotta go|got to go|see ya|catch you later|until next time)\b/i
      ],
      confidence: 0.9
    },
    thanks: {
      patterns: [
        /\b(thanks|thank you|appreciate it|much appreciated|grateful|cheers)\b/i,
        /\b(thank you so much|thanks a lot|thanks a bunch|many thanks)\b/i
      ],
      confidence: 0.9
    },
    services: {
      patterns: [
        /\b(services|service|what do you do|what can you help|solutions|offerings|capabilities)\b/i,
        /\b(web development|website|chatbot|ai|development|programming|coding|software)\b/i,
        /\b(tell me about|show me|learn about|more about|information about)\b.*\b(services|solutions)\b/i
      ],
      confidence: 0.7
    },
    prices: {
      patterns: [
        /\b(prices|pricing|price|cost|costs|how much|expensive|cheap|budget|rate|rates|fee|fees|charge|charges)\b/i,
        /\b(what does it cost|how much does|price range|pricing info|cost estimate|pricing structure)\b/i,
        /\b(packages|plans|pricing plans|service packages)\b/i
      ],
      confidence: 0.7
    },
    booking: {
      patterns: [
        /\b(book|schedule|reserve|appointment|meeting|consultation|call|demo|talk|discuss)\b/i,
        /\b(set up|arrange|plan|organize|when can we|available time|free time|calendar)\b/i,
        /\b(book meeting|schedule meeting|book consultation|free consultation)\b/i
      ],
      confidence: 0.7
    },
    human_help: {
      patterns: [
        /\b(speak|talk|connect|chat|contact|representative|person|human|agent|real|actual)\b/i,
        /\b(real person|human being|someone real|actual person|live agent|customer service)\b/i,
        /\b(speak with|talk to|connect with|contact|reach out)\b.*\b(human|person|someone|agent|representative)\b/i,
        /\b(problem|issue|trouble|difficulty|error|bug|broken|not working|help with|assistance with)\b/i,
        /\b(detailed|explain|description|specific|complex|complicated)\b.*\b(problem|issue|question)\b/i,
        /\b(i have problems|i need help|having trouble|experiencing issues|something is wrong)\b/i,
        /\b(create ticket|support ticket|file ticket|submit ticket|open ticket)\b/i
      ],
      confidence: 0.8
    },
    web_development: {
      patterns: [
        /\b(web development|website|web design|frontend|backend|full stack|react|javascript)\b/i,
        /\b(build website|create website|develop website|web app|web application)\b/i
      ],
      confidence: 0.8
    },
    chatbot_service: {
      patterns: [
        /\b(chatbot|chat bot|ai bot|ai assistant|conversational ai|automated chat)\b/i,
        /\b(lead generation|customer service bot|support bot)\b/i
      ],
      confidence: 0.8
    },
    support_service: {
      patterns: [
        /\b(support|maintenance|ongoing support|updates|improvements|help)\b/i,
        /\b(after launch|post launch|continuous|ongoing|regular updates)\b/i
      ],
      confidence: 0.7
    }
  },

  // Detect intent from user message
  detectIntent(message: string): IntentMatch | null {
    const cleanMessage = message.toLowerCase().trim();
    let bestMatch: IntentMatch | null = null;
    let highestConfidence = 0;

    for (const [intentName, intentData] of Object.entries(this.intents)) {
      for (const pattern of intentData.patterns) {
        if (pattern.test(cleanMessage)) {
          const confidence = intentData.confidence;
          if (confidence > highestConfidence) {
            highestConfidence = confidence;
            bestMatch = {
              intent: intentName,
              confidence: confidence,
              originalMessage: message
            };
          }
        }
      }
    }

    return bestMatch;
  },

  // Get appropriate response for detected intent
  getIntentResponse(intentMatch: IntentMatch, conversationContext: ConversationContext = { lastBotMessage: '', messageCount: 0, hasShownCalendly: false }): IntentResponse | null {
    const { intent } = intentMatch;
    const { lastBotMessage } = conversationContext;

    switch (intent) {
      case 'yes':
        // Context-aware yes responses
        if (lastBotMessage && /consultation|meeting|book|schedule/i.test(lastBotMessage)) {
          return {
            type: 'booking_confirmation',
            messages: [
              "Perfect! Let's get you connected with our team. You can book a free 30-minute consultation below:",
              { type: 'calendly' }
            ]
          };
        }
        if (lastBotMessage && /service|help|assist|project|other services|know more/i.test(lastBotMessage)) {
          return {
            type: 'service_interest',
            messages: [
              "Great! Here are our main services:",
              { type: 'serviceOptions' }
            ]
          };
        }
        return {
          type: 'general_affirmation',
          messages: ["Awesome! What can I help you with today?"]
        };

      case 'no':
        if (lastBotMessage && /consultation|meeting|book/i.test(lastBotMessage)) {
          return {
            type: 'no_booking',
            messages: ["No problem! Feel free to browse our services or ask any questions you might have."]
          };
        }
        return {
          type: 'general_negative',
          messages: ["No worries! Is there anything else I can help you with?"]
        };

      case 'greeting':
        const greetings = [
          "Hi there! 👋 I'm the UpZera assistant. We build smart digital tools that move businesses forward. What can I help you with today?",
          "Hello! 😊 Welcome to UpZera. We specialize in web development and AI solutions. How can I assist you?",
          "Hey! Great to see you here. I'm here to help you learn about UpZera's services. What interests you most?"
        ];
        return {
          type: 'greeting_response',
          messages: [
            greetings[Math.floor(Math.random() * greetings.length)],
            { type: 'quickActions' }
          ]
        };

      case 'goodbye':
        const farewells = [
          "Goodbye! Feel free to reach out anytime. 👋",
          "Thanks for chatting! Book a consultation if you need anything else.",
          "Have a great day! We're here when you're ready to build something amazing."
        ];
        return {
          type: 'farewell',
          messages: [farewells[Math.floor(Math.random() * farewells.length)]],
          endConversation: true
        };

      case 'thanks':
        const thankResponses = [
          "You're very welcome! Anything else I can help you with?",
          "Happy to help! Feel free to ask about our services anytime.",
          "My pleasure! Is there anything specific you'd like to know about UpZera?"
        ];
        return {
          type: 'thanks_response',
          messages: [thankResponses[Math.floor(Math.random() * thankResponses.length)]]
        };

      case 'booking':
        return {
          type: 'booking_intent',
          messages: [
            "Perfect! Let's get you connected with our team. You can book a free 30-minute consultation below:",
            { type: 'calendly' },
            "Or let me know your preferred date and time, and I'll help you set it up!"
          ]
        };

      case 'prices':
        return {
          type: 'pricing_intent',
          messages: [
            "Great question! Our pricing varies based on your specific needs. Here's a quick overview:",
            "💰 Web Development: Starting from €2,500\n💰 AI Chatbots: Starting from €1,500\n💰 Custom Solutions: Let's discuss your requirements",
            "Would you like to schedule a free consultation to get a detailed quote for your project?"
          ]
        };

      case 'human_help':
        return {
          type: 'detailed_support_request',
          messages: [
            "I'd be happy to connect you with our team! I'll help you create a support ticket so our team can provide personalized assistance.",
            "To get started, could you please tell me your name?"
          ]
        };

      default:
        return null;
    }
  }
};
